# Docker Build Service

一个简单的Python服务，用于Docker镜像的拉取推送和从文件构建镜像。

## 功能特性

- **镜像同步**: 从源registry拉取镜像，推送到 `registry.datasecchk.net/library`
- **构建镜像**: 从上传的文件构建Docker镜像，推送到 `registry.datasecchk.net/artifacts`
- **健康检查**: 服务状态监控

## 快速开始

### 1. 配置环境

复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的registry认证信息：
```bash
REGISTRY_USERNAME=your_username
REGISTRY_PASSWORD=your_password
```

### 2. 使用Docker Compose启动

```bash
docker-compose up -d
```

服务将在 `http://localhost:8000` 启动。

### 3. API文档

启动后访问 `http://localhost:8000/docs` 查看自动生成的API文档。

## API接口

### 健康检查
```bash
GET /api/health
```

### 镜像同步
```bash
POST /api/mirror
Content-Type: application/json

{
    "source_image": "nginx:latest",
    "target_image": "my-nginx",
    "tag": "v1.0"
}
```

### 构建镜像
```bash
POST /api/build
Content-Type: multipart/form-data

files: [文件1, 文件2, ...]
image_name: my-app
tag: v1.0
base_image: alpine:latest (可选)
```

### 服务信息
```bash
GET /api/info
```

## 使用示例

### 1. 镜像同步

```bash
curl -X POST "http://localhost:8000/api/mirror" \
  -H "Content-Type: application/json" \
  -d '{
    "source_image": "nginx:latest",
    "target_image": "my-nginx",
    "tag": "v1.0"
  }'
```

### 2. 构建镜像

```bash
curl -X POST "http://localhost:8000/api/build" \
  -F "files=@app.py" \
  -F "files=@requirements.txt" \
  -F "image_name=my-python-app" \
  -F "tag=v1.0" \
  -F "base_image=python:3.11-slim"
```

## 配置说明

| 环境变量 | 默认值 | 说明 |
|---------|--------|------|
| REGISTRY_USERNAME | - | Registry用户名 |
| REGISTRY_PASSWORD | - | Registry密码 |
| TEMP_DIR | ./temp | 临时文件目录 |
| DEFAULT_BASE_IMAGE | alpine:latest | 默认基础镜像 |
| MAX_FILE_SIZE | 104857600 | 最大文件大小(100MB) |
| DOCKER_TIMEOUT | 300 | Docker操作超时时间(秒) |

## 开发模式

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 项目结构

```
docker-service/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI应用
│   ├── docker_service.py    # Docker操作服务
│   └── config.py            # 配置管理
├── templates/
│   └── Dockerfile.template  # Dockerfile模板
├── temp/                    # 临时文件目录
├── requirements.txt         # Python依赖
├── Dockerfile              # 服务镜像
├── docker-compose.yml      # Docker Compose配置
└── README.md
```

## 注意事项

1. 确保Docker daemon正在运行
2. 服务需要访问Docker socket (`/var/run/docker.sock`)
3. 确保有足够的磁盘空间用于镜像构建
4. Registry认证信息请妥善保管

## 故障排除

### 常见问题

1. **Docker连接失败**: 检查Docker daemon是否运行
2. **Registry认证失败**: 检查用户名密码是否正确
3. **文件上传失败**: 检查文件大小是否超过限制
4. **构建超时**: 调整 `DOCKER_TIMEOUT` 配置
