import docker
import os
import shutil
import tempfile
import logging
from typing import Optional, List
from pathlib import Path
from .config import settings

logger = logging.getLogger(__name__)


class DockerService:
    def __init__(self):
        self.client = docker.from_env()
        self.ensure_temp_dir()
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        os.makedirs(settings.temp_dir, exist_ok=True)
    
    def login_registry(self, registry_url: str) -> bool:
        """登录到registry"""
        try:
            if settings.registry_username and settings.registry_password:
                self.client.login(
                    username=settings.registry_username,
                    password=settings.registry_password,
                    registry=registry_url
                )
                logger.info(f"Successfully logged in to {registry_url}")
                return True
            else:
                logger.warning("No registry credentials provided")
                return False
        except Exception as e:
            logger.error(f"Failed to login to registry {registry_url}: {e}")
            return False
    
    def pull_and_push_image(self, source_image: str, target_image: str, tag: str = "latest") -> dict:
        """拉取镜像并推送到目标registry"""
        try:
            # 1. 拉取源镜像
            logger.info(f"Pulling image: {source_image}")
            image = self.client.images.pull(source_image)
            
            # 2. 构建目标镜像名称
            target_full_name = f"{settings.library_registry}/{target_image}:{tag}"
            
            # 3. 重新tag镜像
            image.tag(target_full_name)
            logger.info(f"Tagged image as: {target_full_name}")
            
            # 4. 登录并推送到目标registry
            self.login_registry(settings.library_registry)
            
            logger.info(f"Pushing image: {target_full_name}")
            push_logs = []
            for line in self.client.images.push(target_full_name, stream=True, decode=True):
                if 'status' in line:
                    push_logs.append(line['status'])
                    logger.info(line['status'])
            
            # 5. 清理本地镜像
            try:
                self.client.images.remove(image.id, force=True)
                self.client.images.remove(target_full_name, force=True)
            except:
                pass  # 忽略清理错误
            
            return {
                "success": True,
                "source_image": source_image,
                "target_image": target_full_name,
                "message": "Image pulled and pushed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to pull and push image: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to pull and push image"
            }
    
    def build_from_files(self, files_data: List[tuple], image_name: str, tag: str = "latest", 
                        base_image: Optional[str] = None) -> dict:
        """从文件构建Docker镜像"""
        build_dir = None
        try:
            # 1. 创建临时构建目录
            build_dir = tempfile.mkdtemp(dir=settings.temp_dir)
            logger.info(f"Created build directory: {build_dir}")
            
            # 2. 保存上传的文件
            for filename, file_content in files_data:
                file_path = os.path.join(build_dir, filename)
                # 确保目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"Saved file: {filename}")
            
            # 3. 生成Dockerfile
            dockerfile_content = self.generate_dockerfile(base_image or settings.default_base_image)
            dockerfile_path = os.path.join(build_dir, 'Dockerfile')
            with open(dockerfile_path, 'w') as f:
                f.write(dockerfile_content)
            
            # 4. 构建镜像
            target_full_name = f"{settings.artifacts_registry}/{image_name}:{tag}"
            logger.info(f"Building image: {target_full_name}")
            
            image, build_logs = self.client.images.build(
                path=build_dir,
                tag=target_full_name,
                rm=True,
                timeout=settings.docker_timeout
            )
            
            # 5. 登录并推送镜像
            self.login_registry(settings.artifacts_registry)
            
            logger.info(f"Pushing image: {target_full_name}")
            push_logs = []
            for line in self.client.images.push(target_full_name, stream=True, decode=True):
                if 'status' in line:
                    push_logs.append(line['status'])
                    logger.info(line['status'])
            
            # 6. 清理本地镜像
            try:
                self.client.images.remove(image.id, force=True)
            except:
                pass
            
            return {
                "success": True,
                "image_name": target_full_name,
                "message": "Image built and pushed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to build image from files: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to build image from files"
            }
        finally:
            # 清理临时目录
            if build_dir and os.path.exists(build_dir):
                shutil.rmtree(build_dir, ignore_errors=True)
                logger.info(f"Cleaned up build directory: {build_dir}")
    
    def generate_dockerfile(self, base_image: str) -> str:
        """生成简单的Dockerfile"""
        return f"""FROM {base_image}
                    COPY . /app
                    WORKDIR /app
                    CMD ["sh"]
                """
    
    def health_check(self) -> dict:
        """健康检查"""
        try:
            # 检查Docker daemon连接
            self.client.ping()
            return {
                "status": "healthy",
                "docker": "connected",
                "temp_dir": os.path.exists(settings.temp_dir)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
