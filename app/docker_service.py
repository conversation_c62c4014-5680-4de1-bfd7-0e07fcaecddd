import docker
import os
import shutil
import tempfile
import logging
import subprocess
import json
from typing import Optional, List
from pathlib import Path
from .config import settings

logger = logging.getLogger(__name__)


class DockerService:
    def __init__(self):
        self.client = None
        self.docker_available = self._check_docker_cli()
        if self.docker_available:
            self._init_docker_client()
        self.ensure_temp_dir()

    def _check_docker_cli(self) -> bool:
        """检查Docker CLI是否可用"""
        try:
            # 创建无代理环境
            env = os.environ.copy()
            proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
            for var in proxy_vars:
                if var in env:
                    del env[var]

            result = subprocess.run(['docker', 'version'],
                                  capture_output=True, text=True, env=env, timeout=10)
            if result.returncode == 0:
                logger.info("Docker CLI is available")
                return True
            else:
                logger.error(f"Docker CLI failed: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Docker CLI check failed: {e}")
            return False

    def _run_docker_command(self, cmd: List[str]) -> subprocess.CompletedProcess:
        """运行Docker命令，不使用代理"""
        env = os.environ.copy()
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_vars:
            if var in env:
                del env[var]

        return subprocess.run(cmd, capture_output=True, text=True, env=env, timeout=300)

    def _init_docker_client(self):
        """初始化Docker客户端 - 仅用于复杂操作"""
        # 暂时不初始化docker-py客户端，因为代理问题
        # 我们将使用CLI命令来实现功能
        logger.info("Using Docker CLI instead of Python API due to proxy issues")
    
    def ensure_temp_dir(self):
        """确保临时目录存在"""
        os.makedirs(settings.temp_dir, exist_ok=True)

    def _check_docker_available(self):
        """检查Docker是否可用"""
        if not self.docker_available:
            raise Exception("Docker is not available. Please check Docker daemon status.")
    
    def login_registry(self, registry_url: str) -> bool:
        """登录到registry"""
        try:
            if settings.registry_username and settings.registry_password:
                self.client.login(
                    username=settings.registry_username,
                    password=settings.registry_password,
                    registry=registry_url
                )
                logger.info(f"Successfully logged in to {registry_url}")
                return True
            else:
                logger.warning("No registry credentials provided")
                return False
        except Exception as e:
            logger.error(f"Failed to login to registry {registry_url}: {e}")
            return False
    
    def pull_and_push_image(self, source_image: str, target_image: str, tag: str = "latest") -> dict:
        """拉取镜像并推送到目标registry"""
        try:
            self._check_docker_available()
            # 1. 拉取源镜像
            logger.info(f"Pulling image: {source_image}")
            result = self._run_docker_command(['docker', 'pull', source_image])
            if result.returncode != 0:
                raise Exception(f"Failed to pull image: {result.stderr}")
            
            # 2. 构建目标镜像名称
            target_full_name = f"{settings.library_registry}/{target_image}:{tag}"
            
            # 3. 重新tag镜像
            logger.info(f"Tagging image as: {target_full_name}")
            result = self._run_docker_command(['docker', 'tag', source_image, target_full_name])
            if result.returncode != 0:
                raise Exception(f"Failed to tag image: {result.stderr}")

            # 4. 登录到目标registry
            if settings.registry_username and settings.registry_password:
                logger.info(f"Logging in to {settings.library_registry}")
                result = self._run_docker_command([
                    'docker', 'login', settings.library_registry,
                    '-u', settings.registry_username,
                    '-p', settings.registry_password
                ])
                if result.returncode != 0:
                    logger.warning(f"Login failed: {result.stderr}")

            # 5. 推送镜像
            logger.info(f"Pushing image: {target_full_name}")
            result = self._run_docker_command(['docker', 'push', target_full_name])
            if result.returncode != 0:
                raise Exception(f"Failed to push image: {result.stderr}")

            # 6. 清理本地镜像
            try:
                self._run_docker_command(['docker', 'rmi', source_image, target_full_name])
            except:
                pass  # 忽略清理错误
            
            return {
                "success": True,
                "source_image": source_image,
                "target_image": target_full_name,
                "message": "Image pulled and pushed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to pull and push image: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to pull and push image"
            }
    
    def build_from_files(self, files_data: List[tuple], image_name: str, tag: str = "latest",
                        base_image: Optional[str] = None) -> dict:
        """从文件构建Docker镜像"""
        build_dir = None
        try:
            self._check_docker_client()
            # 1. 创建临时构建目录
            build_dir = tempfile.mkdtemp(dir=settings.temp_dir)
            logger.info(f"Created build directory: {build_dir}")
            
            # 2. 保存上传的文件
            for filename, file_content in files_data:
                file_path = os.path.join(build_dir, filename)
                # 确保目录存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"Saved file: {filename}")
            
            # 3. 生成Dockerfile
            dockerfile_content = self.generate_dockerfile(base_image or settings.default_base_image)
            dockerfile_path = os.path.join(build_dir, 'Dockerfile')
            with open(dockerfile_path, 'w') as f:
                f.write(dockerfile_content)
            
            # 4. 构建镜像
            target_full_name = f"{settings.artifacts_registry}/{image_name}:{tag}"
            logger.info(f"Building image: {target_full_name}")
            
            image, build_logs = self.client.images.build(
                path=build_dir,
                tag=target_full_name,
                rm=True,
                timeout=settings.docker_timeout
            )
            
            # 5. 登录并推送镜像
            self.login_registry(settings.artifacts_registry)
            
            logger.info(f"Pushing image: {target_full_name}")
            push_logs = []
            for line in self.client.images.push(target_full_name, stream=True, decode=True):
                if 'status' in line:
                    push_logs.append(line['status'])
                    logger.info(line['status'])
            
            # 6. 清理本地镜像
            try:
                self.client.images.remove(image.id, force=True)
            except:
                pass
            
            return {
                "success": True,
                "image_name": target_full_name,
                "message": "Image built and pushed successfully"
            }
            
        except Exception as e:
            logger.error(f"Failed to build image from files: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to build image from files"
            }
        finally:
            # 清理临时目录
            if build_dir and os.path.exists(build_dir):
                shutil.rmtree(build_dir, ignore_errors=True)
                logger.info(f"Cleaned up build directory: {build_dir}")
    
    def generate_dockerfile(self, base_image: str) -> str:
        """生成简单的Dockerfile"""
        return f"""FROM {base_image}
                    COPY . /app
                    WORKDIR /app
                    CMD ["sh"]
                """
    
    def health_check(self) -> dict:
        """健康检查"""
        try:
            # 检查Docker客户端是否存在
            if self.client is None:
                return {
                    "status": "unhealthy",
                    "docker": "not_connected",
                    "error": "Docker client not initialized"
                }

            # 检查Docker daemon连接
            self.client.ping()
            return {
                "status": "healthy",
                "docker": "connected",
                "temp_dir": os.path.exists(settings.temp_dir)
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "docker": "connection_failed",
                "error": str(e)
            }
