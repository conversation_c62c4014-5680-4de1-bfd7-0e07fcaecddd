from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional
import logging
import os

try:
    # 当作为模块运行时使用相对导入
    from .docker_service import DockerService
    from .config import settings
except ImportError:
    # 当直接运行时使用绝对导入
    from docker_service import DockerService
    from config import settings

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Docker Build Service",
    description="Simple service to pull/push Docker images and build images from files",
    version="1.0.0"
)

# 初始化Docker服务
docker_service = DockerService()


class PullPushRequest(BaseModel):
    source_image: str
    target_image: str
    tag: str = "latest"


@app.get("/")
async def root():
    return {"message": "Docker Build Service is running"}


@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    try:
        result = docker_service.health_check()
        if result["status"] == "healthy":
            return JSONResponse(content=result, status_code=200)
        else:
            return JSONResponse(content=result, status_code=503)
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            content={"status": "unhealthy", "error": str(e)}, 
            status_code=503
        )


@app.post("/api/mirror")
async def mirror_image(request: PullPushRequest):
    """镜像同步：拉取镜像并推送到library registry"""
    try:
        logger.info(f"Mirror request: {request.source_image} -> {request.target_image}:{request.tag}")
        
        result = docker_service.pull_and_push_image(
            source_image=request.source_image,
            target_image=request.target_image,
            tag=request.tag
        )
        
        if result["success"]:
            return JSONResponse(content=result, status_code=200)
        else:
            return JSONResponse(content=result, status_code=400)
            
    except Exception as e:
        logger.error(f"Mirror failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/build")
async def build_image(
    files: List[UploadFile] = File(...),
    image_name: str = Form(...),
    tag: str = Form(default="latest"),
    base_image: Optional[str] = Form(default=None)
):
    """构建镜像：从上传的文件构建Docker镜像并推送到artifacts registry"""
    try:
        logger.info(f"Build request: {image_name}:{tag} with {len(files)} files")
        
        # 验证文件
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")
        
        # 读取文件内容
        files_data = []
        total_size = 0
        
        for file in files:
            if not file.filename:
                continue
                
            content = await file.read()
            total_size += len(content)
            
            # 检查总文件大小
            if total_size > settings.max_file_size:
                raise HTTPException(
                    status_code=413, 
                    detail=f"Total file size exceeds limit ({settings.max_file_size} bytes)"
                )
            
            files_data.append((file.filename, content))
            logger.info(f"Read file: {file.filename} ({len(content)} bytes)")
        
        if not files_data:
            raise HTTPException(status_code=400, detail="No valid files found")
        
        # 构建镜像
        result = docker_service.build_from_files(
            files_data=files_data,
            image_name=image_name,
            tag=tag,
            base_image=base_image
        )
        
        if result["success"]:
            return JSONResponse(content=result, status_code=200)
        else:
            return JSONResponse(content=result, status_code=400)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Build from files failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/info")
async def get_info():
    """获取服务信息"""
    return {
        "service": "Docker Build Service",
        "version": "1.0.0",
        "registries": {
            "library": settings.library_registry,
            "artifacts": settings.artifacts_registry
        },
        "config": {
            "max_file_size": settings.max_file_size,
            "default_base_image": settings.default_base_image,
            "temp_dir": settings.temp_dir
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
