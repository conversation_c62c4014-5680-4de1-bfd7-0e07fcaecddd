from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    # Registry配置
    library_registry: str = "registry.datasecchk.net/library"
    artifacts_registry: str = "registry.datasecchk.net/artifacts"
    registry_username: Optional[str] = None
    registry_password: Optional[str] = None
    
    # 服务配置
    temp_dir: str = "./temp"
    default_base_image: str = "alpine:latest"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    
    # Docker配置
    docker_timeout: int = 300  # 5分钟超时
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


settings = Settings()
